/**
 * @file pages/cluster/role/components/role-rules-table.js
 * <AUTHOR> Console
 * @description 规则列表表格组件
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Table} from '@baidu/sui';
import './style.less';

const {asComponent, invokeAppComp, invokeBceSanUI} = decorators;

const template = html`
    <template>
        <div class="role-rules-table">
            <s-table columns="{{columns}}" datasource="{{rules}}" pagination="{{false}}">
                <div slot="c-resources">{{row.resources | formatArray}}</div>

                <div slot="c-verbs">{{row.verbs | formatArray}}</div>

                <div slot="c-nonResourceURLs">{{row.nonResourceURLs | formatArray}}</div>

                <div slot="c-resourceNames">{{row.resourceNames | formatArray}}</div>

                <div slot="c-apiGroups">{{row.apiGroups | formatApiGroups}}</div>
            </s-table>
        </div>
    </template>
`;

@asComponent('@role-rules-table')
@invokeAppComp
@invokeBceSanUI
export default class RoleRulesTable extends Component {
    static template = template;
    static components = {
        's-table': Table,
    };

    static filters = {
        formatArray(arr) {
            if (!arr || !Array.isArray(arr) || arr.length === 0) {
                return '-';
            }
            return arr.join(', ');
        },
        formatApiGroups(apiGroups) {
            if (!apiGroups || !Array.isArray(apiGroups) || apiGroups.length === 0) {
                return '-';
            }
            return apiGroups.map(group => (group === '' ? 'core' : group)).join(', ');
        },
    };

    initData() {
        return {
            rules: [],
            columns: [
                {name: 'resources', label: '资源', width: '150'},
                {name: 'verbs', label: '动作', width: '150'},
                {name: 'nonResourceURLs', label: '非资源 URL', width: '150'},
                {name: 'resourceNames', label: '资源名', width: '150'},
                {name: 'apiGroups', label: 'API 组', width: '120'},
            ],
        };
    }
}
