/**
 * @file pages/cluster/role/components/yaml-dialog.js
 * <AUTHOR> Console
 * @description YAML 编辑/查看弹框组件
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Dialog, Button, Tooltip, Notification} from '@baidu/sui';
import {ACEEditor} from '@baiducloud/bce-ui/san';
import {ClipBoard} from '@baidu/sui-biz';
import {OutlinedCopy} from '@baidu/sui-icon';

const {asComponent, invokeAppComp, invokeBceSanUI} = decorators;

const template = html`
    <template>
        <s-dialog
            class="yaml-dialog"
            open="{=open=}"
            confirming="{=confirming=}"
            title="{{title}}"
            width="1120"
            height="500"
            closeAfterMaskClick="{{false}}"
            on-confirm="onConfirm"
            on-close="onClose"
        >
            <div class="yaml-dialog-content">
                <div class="yaml-toolbar" s-if="mode !== 'create'">
                    <s-tooltip content="复制">
                        <s-clip-board text="{{content}}">
                            <s-button class="copy-btn">
                                <s-icon-copy />
                            </s-button>
                        </s-clip-board>
                    </s-tooltip>
                </div>
                <ui-aceeditor
                    value="{=content=}"
                    mode="ace/mode/yaml"
                    theme="ace/theme/chrome"
                    height="400"
                    readonly="{{mode === 'view'}}"
                    showPrintMargin="{{false}}"
                    wrap="free"
                />
            </div>
            <div slot="footer" s-if="mode === 'view'">
                <s-button on-click="onClose">关闭</s-button>
            </div>
            <div slot="footer" s-else>
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" on-click="onConfirm">确认</s-button>
            </div>
        </s-dialog>
    </template>
`;

@invokeAppComp
@invokeBceSanUI
export default class YamlDialog extends Component {
    static template = template;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-tooltip': Tooltip,
        's-clip-board': ClipBoard,
        's-icon-copy': OutlinedCopy,
        'ui-aceeditor': ACEEditor,
    };

    initData() {
        return {
            open: false,
            confirming: false,
            mode: 'create', // create | edit | view
            title: '',
            content: '',
            editorOptions: {
                fontSize: 14,
                showPrintMargin: false,
                wrap: true,
            },
        };
    }

    onConfirm() {
        if (this.data.get('mode') === 'view') {
            // 查看模式下，确定按钮直接关闭弹窗
            this.onClose();
            return;
        }

        this.data.set('confirming', true);
        this.fire('confirm');
    }

    onClose() {
        this.data.set('open', false);
        this.data.set('confirming', false);
        this.fire('close');
    }
}
