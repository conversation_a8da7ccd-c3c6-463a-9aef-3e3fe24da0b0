/**
 * @file pages/cluster/role/list/role-list.js
 * <AUTHOR> Console
 * @description Role 列表组件
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {
    Button,
    Table,
    Pagination,
    Search,
    Select,
    Tag,
    Tooltip,
    Notification,
    Dialog,
} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import YamlEditor from '../../../../components/yaml-editor';
import DeleteDialog from '../components/delete-dialog';
import {utcToTime} from '../../../../utils/util';
import jsyaml from 'js-yaml';
import _ from 'lodash';
import './style.less';

const {asComponent, invokeAppComp, invokeBceSanUI} = decorators;

const template = html`
    <template>
        <div class="role-list">
            <div class="role-filter">
                <div class="create">
                    <s-button skin="primary" on-click="onCreate">创建</s-button>
                </div>
                <div class="refresh">
                    <s-button class="s-icon-button" on-click="onRefresh">
                        <s-icon-refresh is-button="{{false}}" />
                    </s-button>
                </div>
                <div class="name">
                    <s-search
                        value="{=searchValue=}"
                        on-search="onSearch"
                        placeholder="请输入资源名称搜索"
                    />
                </div>
                <div class="namespace">
                    <s-select
                        value="{=namespace=}"
                        datasource="{{namespaceList}}"
                        width="200"
                        placeholder="全部命名空间"
                        on-change="onNamespaceChange"
                        searchable
                    />
                </div>
            </div>

            <!-- 列表表格 -->
            <div class="role-list-table">
                <s-table columns="{{columns}}" datasource="{{datasource}}" loading="{{loading}}">
                    <div slot="c-action">
                        <s-button s-if="row.isSystemRole" skin="stringfy" on-click="onViewYaml(row)"
                            >查看YAML</s-button
                        >
                        <s-button s-else skin="stringfy" on-click="onEditYaml(row)"
                            >编辑YAML</s-button
                        >

                        <s-tooltip s-if="row.isSystemRole" content="系统默认角色不支持删除">
                            <s-button skin="stringfy" disabled>删除</s-button>
                        </s-tooltip>
                        <s-button s-else skin="stringfy" on-click="onDelete(row)">删除</s-button>
                    </div>
                </s-table>
            </div>

            <!-- 分页 -->
            <s-pagination
                s-if="totalCount > 0"
                total="{{totalCount}}"
                pageSize="{{pageSize}}"
                pageSizes="{{[20, 50, 100]}}"
                layout="pageSize,pager,go"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />

            <!-- YAML 弹框 -->
            <s-dialog
                class="yaml-dialog"
                open="{=yamlDialogOpen=}"
                confirming="{=confirming=}"
                title="{{yamlDialogTitle}}"
                width="1120"
                height="600"
                closeAfterMaskClick="{{false}}"
                on-confirm="onYamlConfirm"
                on-close="onYamlClose"
            >
                <yaml-editor value="{=yamlContent=}" height="{{450}}" enhanced="{{true}}" />
                <div slot="footer" s-if="yamlDialogMode === 'view'">
                    <s-button on-click="onYamlClose">关闭</s-button>
                </div>
                <div slot="footer" s-else>
                    <s-button on-click="onYamlClose">取消</s-button>
                    <s-button skin="primary" on-click="onYamlConfirm">确认</s-button>
                </div>
            </s-dialog>

            <!-- 删除确认弹框 -->
            <delete-dialog
                s-ref="deleteDialog"
                on-confirm="onDeleteConfirm"
                on-close="onDeleteClose"
            />
        </div>
    </template>
`;

@asComponent('@role-list')
@invokeAppComp
@invokeBceSanUI
export default class RoleList extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        's-search': Search,
        's-select': Select,
        's-tag': Tag,
        's-tooltip': Tooltip,
        's-icon-refresh': OutlinedRefresh,
        's-dialog': Dialog,
        'yaml-editor': YamlEditor,
        'delete-dialog': DeleteDialog,
    };

    static filters = {
        formatTime: utcToTime,
    };

    // 系统默认角色列表 - Role 资源只有 cce:admin 为系统默认资源
    static SYSTEM_ROLES = ['cce:admin'];

    initData() {
        return {
            loading: false,
            datasource: [],
            totalCount: 0,
            pageNo: 1,
            pageSize: 20,
            searchValue: '',
            namespace: '', // 命名空间筛选
            namespaceList: [{text: '全部命名空间', value: ''}], // 命名空间列表
            clusterUuid: '',
            yamlDialogOpen: false,
            yamlDialogMode: 'view',
            yamlDialogTitle: '',
            yamlContent: '',
            currentEditRow: null,
            confirming: false,

            // 表格列配置
            columns: [
                {
                    name: 'name',
                    label: '名称',
                    width: 300,
                    render(item) {
                        const name = item?.metadata?.name || '-';
                        // 使用已经计算好的 isSystemRole 字段
                        const tag = item.isSystemRole
                            ? '<span class="system-role-tag" style="margin-left: 8px; padding: 2px 6px; background: #e6f7ff; color: #1890ff; border-radius: 2px; font-size: 12px; border: 1px solid #91d5ff;">系统默认角色</span>'
                            : '';

                        // 构建详情链接
                        const urlParams = new URLSearchParams(window.location.hash.split('?')[1]);
                        const clusterUuid = urlParams.get('clusterUuid');
                        const namespace = item?.metadata?.namespace || 'default';
                        const detailUrl = `#/cce/cluster/role/detail?clusterUuid=${clusterUuid}&name=${item?.metadata?.name}&namespace=${namespace}&type=role`;

                        return `<a href="${detailUrl}">${name}</a>${tag}`;
                    },
                },
                {
                    name: 'namespace',
                    label: '命名空间',
                    width: 150,
                    render(item) {
                        return item?.metadata?.namespace || '-';
                    },
                },
                {
                    name: 'createTime',
                    label: '创建时间',
                    width: 200,
                    render(item) {
                        return utcToTime(item?.metadata?.creationTimestamp) || '-';
                    },
                },
                {
                    name: 'action',
                    label: '操作',
                    width: 200,
                    slot: 'action',
                },
            ],
        };
    }

    attached() {
        // 从父组件获取集群信息
        const clusterUuid = this.data.get('clusterUuid');

        if (clusterUuid) {
            this.data.set('clusterUuid', clusterUuid);
            this.loadNamespaceList(); // 加载命名空间列表
            this.loadData();
        }
    }

    // 监听属性变化
    static computed = {
        // 可以添加计算属性
    };

    // 加载列表数据
    async loadData() {
        try {
            this.data.set('loading', true);
            const {clusterUuid, pageNo, pageSize, searchValue, namespace} = this.data.get();

            // 如果没有 clusterUuid，直接返回
            if (!clusterUuid) {
                return;
            }

            const params = {
                group: 'rbac.authorization.k8s.io',
                version: 'v1',
                kind: 'Role',
                namespace: namespace || '', // 支持按命名空间筛选，空值表示获取所有
                clusterUuid,
                pageNo,
                pageSize,
                name: searchValue?.trim(),
            };

            const response = await this.$http.getResourceObjList(params);

            const items = (response?.result?.items || []).map(item => {
                try {
                    const yamlData = typeof item === 'string' ? JSON.parse(item) : item;
                    // 创建包装对象，将 YAML 数据和 UI 字段分开
                    return {
                        // 原始 YAML 数据
                        yamlData: yamlData,
                        // UI 相关字段
                        isSystemRole: RoleList.SYSTEM_ROLES.includes(yamlData?.metadata?.name),
                        // 为了兼容现有代码，保留对 metadata 的直接访问
                        metadata: yamlData?.metadata,
                        // 其他可能需要的字段
                        kind: yamlData?.kind,
                        apiVersion: yamlData?.apiVersion,
                    };
                } catch (error) {
                    return {
                        yamlData: {},
                        isSystemRole: false,
                        metadata: {},
                    };
                }
            });

            this.data.set('datasource', items);
            this.data.set('totalCount', response?.result?.totalCount || 0);
        } catch (error) {
            Notification.error('加载列表失败');
        } finally {
            this.data.set('loading', false);
        }
    }

    // 加载命名空间列表
    async loadNamespaceList() {
        const {clusterUuid} = this.data.get();
        try {
            const {result} = await this.$http.listAppNamespace({clusterUuid});
            let namespaceList = _.uniq(
                _.map(_.get(result, 'namespaces', []), item => _.get(item, 'objectMeta.name', '')),
            );
            namespaceList = _.map(namespaceList, item => ({
                text: item,
                value: item,
                title: item,
            }));
            namespaceList.unshift({text: '全部命名空间', value: ''});
            this.data.set('namespaceList', namespaceList);
        } catch (e) {
            // 获取命名空间列表失败，使用默认值
        }
    }

    // 命名空间变更
    onNamespaceChange(e) {
        // 直接从事件参数中获取最新的选中值，避免延迟问题
        const selectedNamespace = e.value;
        this.data.set('namespace', selectedNamespace);
        this.data.set('pageNo', 1);
        this.loadData();
    }

    // 搜索
    onSearch() {
        this.data.set('pageNo', 1);
        this.loadData();
    }

    // 刷新
    onRefresh() {
        this.loadNamespaceList(); // 刷新时也重新加载命名空间列表
        this.loadData();
    }

    // 分页变化
    onPageChange({value}) {
        this.data.set('pageNo', value.page);
        this.loadData();
    }

    onPageSizeChange({value}) {
        this.data.set('pageSize', value.pageSize);
        this.data.set('pageNo', 1);
        this.loadData();
    }

    // 创建 YAML
    onCreate() {
        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'create');
        this.data.set('yamlDialogTitle', '创建 Role');
        this.data.set('currentEditRow', null);
        // 设置默认的 Role YAML 模板
        const defaultYaml = `apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: example-role
  namespace: default
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]`;
        this.data.set('yamlContent', defaultYaml);
    }

    // 编辑 YAML
    onEditYaml(row) {
        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'edit');
        this.data.set('yamlDialogTitle', '编辑 YAML');
        this.data.set('currentEditRow', row);
        try {
            // 使用原始 YAML 数据而不是包装对象
            this.data.set('yamlContent', jsyaml.safeDump(row.yamlData));
        } catch (error) {
            Notification.error('YAML 序列化失败');
        }
    }

    // 查看 YAML（参考 resource-obj 的实现）
    onViewYaml(row) {
        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'view');
        this.data.set('yamlDialogTitle', '查看 YAML');
        this.data.set('currentEditRow', row);
        try {
            // 使用原始 YAML 数据而不是包装对象
            this.data.set('yamlContent', jsyaml.safeDump(row.yamlData));
        } catch (error) {
            Notification.error('YAML 序列化失败');
        }
    }

    // 删除
    onDelete(row) {
        const resourceName = row.metadata?.name;
        const createTime = utcToTime(row.metadata?.creationTimestamp);
        const namespace = row.metadata?.namespace;
        this.data.set('currentEditRow', row);
        // 传递 namespace 信息给删除对话框，用于区分 role 和 clusterrole
        this.ref('deleteDialog').show(resourceName, createTime, namespace, 'role');
    }

    // YAML 弹框确认
    async onYamlConfirm() {
        if (this.data.get('yamlDialogMode') === 'view') {
            // 查看模式下，确定按钮直接关闭弹窗
            this.onYamlClose();
            return;
        }

        try {
            this.data.set('confirming', true);
            const {yamlContent, yamlDialogMode, clusterUuid, currentEditRow} = this.data.get();
            const yamlData = jsyaml.load(yamlContent);

            if (yamlDialogMode === 'create') {
                await this.$http.createResource({
                    clusterUuid,
                    content: yamlContent,
                    namespace: yamlData?.metadata?.namespace || 'default',
                    validate: true,
                });
                Notification.success('创建成功');
            } else if (yamlDialogMode === 'edit') {
                const params = {
                    group: 'rbac.authorization.k8s.io',
                    version: 'v1',
                    kind: 'Role',
                    name: currentEditRow.metadata?.name,
                    namespace: currentEditRow.metadata?.namespace,
                    clusterUuid,
                };
                await this.$http.editResource(params, yamlData);
                Notification.success('编辑成功');
            }

            this.onYamlClose();
            this.loadData();
        } catch (error) {
            Notification.error(error.message || '操作失败');
            this.data.set('confirming', false);
        }
    }

    // YAML 弹框关闭
    onYamlClose() {
        this.data.set('yamlDialogOpen', false);
        this.data.set('yamlContent', '');
        this.data.set('currentEditRow', null);
        this.data.set('confirming', false);
    }

    // 删除确认
    async onDeleteConfirm() {
        try {
            const {clusterUuid, currentEditRow} = this.data.get();
            const params = {
                kind: 'Role',
                clusterUuid,
                resourceList: [
                    {
                        kind: 'Role',
                        group: 'rbac.authorization.k8s.io',
                        version: 'v1',
                        namespace: currentEditRow.metadata?.namespace,
                        name: currentEditRow.metadata?.name,
                    },
                ],
                method: 'delete',
            };

            await this.$http.deleteResource(params);
            this.ref('deleteDialog').data.set('confirming', false);
            this.ref('deleteDialog').data.set('open', false);
            this.loadData();
            Notification.success('删除成功');
        } catch (error) {
            Notification.error(error.message || '删除失败');
            this.ref('deleteDialog').data.set('confirming', false);
        }
    }

    // 删除关闭
    onDeleteClose() {
        this.data.set('currentEditRow', null);
    }
}
