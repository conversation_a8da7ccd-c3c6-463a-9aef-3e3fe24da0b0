/**
 * @file pages/cluster/role/detail/detail.js
 * <AUTHOR> Console
 * @description ClusterRole 详情组件
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Button, Loading, Notification} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {Legend} from '@baidu/sui-biz';
import YamlDialog from '../components/yaml-dialog';
import RoleRulesTable from '../components/role-rules-table';
import {utcToTime} from '../../../../utils/util';
import jsyaml from 'js-yaml';
import './style.less';

const {asComponent, invokeAppComp, invokeBceSanUI} = decorators;

const template = html`
    <template>
        <div class="cluster-role-detail">
            <s-loading s-if="loading" />
            <div s-else>
                <!-- 操作按钮 -->
                <div class="detail-operations">
                    <s-button s-if="isSystemRole" on-click="onViewYaml"> 查看 YAML </s-button>
                    <s-button s-else on-click="onEditYaml"> 编辑 YAML </s-button>
                    <s-button class="refresh-btn" on-click="onRefresh">
                        <s-icon-refresh />
                        刷新
                    </s-button>
                </div>

                <!-- 基本信息 -->
                <s-legend label="基本信息" class="basic-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">名称：</span>
                            <span class="info-value">{{detail.metadata.name}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">创建时间：</span>
                            <span class="info-value"
                                >{{detail.metadata.creationTimestamp | formatTime}}</span
                            >
                        </div>
                        <div class="info-item">
                            <span class="info-label">UUID：</span>
                            <span class="info-value">{{detail.metadata.uid}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">标签：</span>
                            <span class="info-value"
                                >{{getLabelsText(detail.metadata.labels)}}</span
                            >
                        </div>
                        <div class="info-item">
                            <span class="info-label">注解：</span>
                            <span class="info-value"
                                >{{getAnnotationsText(detail.metadata.annotations)}}</span
                            >
                        </div>
                    </div>
                </s-legend>

                <!-- 规则 -->
                <s-legend label="规则" class="rules-info">
                    <role-rules-table rules="{{detail.rules}}" />
                </s-legend>
            </div>

            <!-- YAML 弹框 -->
            <yaml-dialog
                open="{=yamlDialogOpen=}"
                mode="{{yamlDialogMode}}"
                title="{{yamlDialogTitle}}"
                content="{=yamlContent=}"
                on-confirm="onYamlConfirm"
                on-close="onYamlClose"
            />
        </div>
    </template>
`;

@asComponent('@cluster-role-detail')
@invokeAppComp
@invokeBceSanUI
export default class ClusterRoleDetail extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-loading': Loading,
        's-legend': Legend,
        's-icon-refresh': OutlinedRefresh,
        'yaml-dialog': YamlDialog,
        'role-rules-table': RoleRulesTable,
    };

    static filters = {
        formatTime: utcToTime,
    };

    initData() {
        return {
            loading: false,
            detail: {},
            yamlDialogOpen: false,
            yamlDialogMode: 'view',
            yamlDialogTitle: '',
            yamlContent: '',
            systemRoles: ['cce:admin'],
        };
    }

    static computed = {
        isSystemRole() {
            const detail = this.data.get('detail');
            const systemRoles = this.data.get('systemRoles');
            return systemRoles.includes(detail.metadata?.name);
        },
    };

    attached() {
        this.loadDetail();
    }

    // 加载详情数据
    async loadDetail() {
        try {
            this.data.set('loading', true);
            const {clusterUuid, roleName} = this.data.get();

            const params = {
                group: 'rbac.authorization.k8s.io',
                version: 'v1',
                kind: 'ClusterRole',
                namespace: '',
                clusterUuid,
                pageNo: 1,
                pageSize: 1000,
                name: roleName,
            };

            const response = await this.$http.getResourceObjList(params);
            const items = (response?.result?.items || []).map(item => {
                try {
                    return JSON.parse(item);
                } catch (error) {
                    return {};
                }
            });

            const detail = items.find(item => item.metadata?.name === roleName);
            if (detail) {
                this.data.set('detail', detail);
            } else {
                Notification.error('未找到指定的 ClusterRole');
            }
        } catch (error) {
            console.error('加载 ClusterRole 详情失败:', error);
            Notification.error('加载详情失败');
        } finally {
            this.data.set('loading', false);
        }
    }

    // 获取标签文本
    getLabelsText(labels) {
        if (!labels || Object.keys(labels).length === 0) {
            return '-';
        }
        return Object.entries(labels)
            .map(([key, value]) => `${key}=${value}`)
            .join(', ');
    }

    // 获取注解文本
    getAnnotationsText(annotations) {
        if (!annotations || Object.keys(annotations).length === 0) {
            return '-';
        }
        return Object.entries(annotations)
            .map(([key, value]) => `${key}=${value}`)
            .join(', ');
    }

    // 刷新
    onRefresh() {
        this.loadDetail();
    }

    // 编辑 YAML
    onEditYaml() {
        try {
            const detail = this.data.get('detail');
            const yamlContent = jsyaml.safeDump(detail);
            this.data.set('yamlDialogOpen', true);
            this.data.set('yamlDialogMode', 'edit');
            this.data.set('yamlDialogTitle', '编辑 YAML');
            this.data.set('yamlContent', yamlContent);
        } catch (error) {
            console.error('转换 YAML 失败:', error);
            Notification.error('转换 YAML 失败');
        }
    }

    // 查看 YAML
    onViewYaml() {
        try {
            const detail = this.data.get('detail');
            const yamlContent = jsyaml.safeDump(detail);
            this.data.set('yamlDialogOpen', true);
            this.data.set('yamlDialogMode', 'view');
            this.data.set('yamlDialogTitle', '查看 YAML');
            this.data.set('yamlContent', yamlContent);
        } catch (error) {
            console.error('转换 YAML 失败:', error);
            Notification.error('转换 YAML 失败');
        }
    }

    // YAML 弹框确认
    async onYamlConfirm() {
        try {
            const {yamlContent, yamlDialogMode, clusterUuid, detail} = this.data.get();
            const yamlData = jsyaml.load(yamlContent);

            if (yamlDialogMode === 'edit') {
                const params = {
                    group: 'rbac.authorization.k8s.io',
                    version: 'v1',
                    kind: 'ClusterRole',
                    name: detail.metadata?.name,
                    clusterUuid,
                };
                await this.$http.editResource(params, yamlData);
                Notification.success('编辑成功');
                this.data.set('yamlDialogOpen', false);
                this.loadDetail();
            }
        } catch (error) {
            console.error('操作失败:', error);
            Notification.error(error.message || '操作失败');
        }
    }

    // YAML 弹框关闭
    onYamlClose() {
        this.data.set('yamlDialogOpen', false);
        this.data.set('yamlContent', '');
    }
}
