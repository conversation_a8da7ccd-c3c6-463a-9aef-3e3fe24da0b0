/**
 * @file pages/cluster/role/detail/index.js
 * <AUTHOR> Console
 * @description ClusterRole 资源详情页面
 */

import _ from 'lodash';
import {AppDetailPage, AppTabPage} from '@baidu/sui-biz';
import {Button, Loading, Notification} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {decorators, html, redirect} from '@baiducloud/runtime';
import ClusterRoleInfoDetail from './cluster-role-info';
import RoleInfoDetail from './role-info';
import YamlDialog from '../components/yaml-dialog';
import jsyaml from 'js-yaml';
import './style.less';

const {asPage, invokeComp} = decorators;

const template = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <div slot="pageTitle" class="app-page-title">
                <cce-page-title title-data="{=titleData=}" />
                <s-button s-if="isSystemRole" class="ml10" on-click="viewYaml">查看YAML</s-button>
                <s-button s-else class="ml10" on-click="editYaml">编辑YAML</s-button>
                <s-button class="refresh-btn" on-click="getDetail"><s-icon-refresh /></s-button>
            </div>
            <s-app-tab-page skin="accordion" active="{=active=}">
                <s-app-tab-page-panel
                    class="{{loading ? 'panel-loading' : ''}}"
                    label="详情"
                    url="#/cce/cluster/role/detail?clusterUuid={{clusterUuid}}&name={{name}}{{namespace ? '&namespace=' + namespace + '&type=role' : '&type=clusterrole'}}"
                >
                    <cluster-role-info-detail
                        s-if="!isRoleType"
                        detail="{{detail}}"
                        on-refresh="getDetail"
                    />
                    <role-info-detail
                        s-if="isRoleType"
                        detail="{{detail}}"
                        on-refresh="getDetail"
                    />
                </s-app-tab-page-panel>
            </s-app-tab-page>
        </s-app-detail-page>

        <!-- YAML 弹框 -->
        <yaml-dialog
            open="{=yamlDialogOpen=}"
            mode="{{yamlDialogMode}}"
            title="{{yamlDialogTitle}}"
            content="{=yamlContent=}"
            on-confirm="onYamlConfirm"
            on-close="onYamlClose"
        />
    </div>
`;

@asPage('/cce/cluster/role/detail')
@invokeComp('@cce-page-title')
export default class ClusterRoleDetailPage extends AppDetailPage {
    static template = template;

    REGION_CHANGE_LOCATION = '#/cce/cluster/list';

    // Role 系统默认角色列表
    static SYSTEM_ROLES_ROLE = ['cce:admin'];

    // ClusterRole 系统默认角色列表 - 包含 Kubernetes 常见的系统 ClusterRole
    static SYSTEM_ROLES_CLUSTERROLE = [
        'admin',
        'cluster-admin',
        'edit',
        'view',
        'system:admin',
        'system:cluster-admin',
        'system:kube-scheduler',
        'system:kube-controller-manager',
        'system:node',
        'system:node-proxier',
    ];

    static components = {
        's-app-detail-page': AppDetailPage,
        's-app-tab-page': AppTabPage,
        's-app-tab-page-panel': AppTabPage.TabPanel,
        's-button': Button,
        'cluster-role-info-detail': ClusterRoleInfoDetail,
        'role-info-detail': RoleInfoDetail,
        'yaml-dialog': YamlDialog,
        's-icon-refresh': OutlinedRefresh,
        's-loading': Loading,
    };

    static computed = {
        isSystemRole() {
            const detail = this.data.get('detail');
            const roleName = detail?.name;
            const routeQuery = this.data.get('route.query');
            const isRoleType = routeQuery?.type === 'role';

            // 根据资源类型使用不同的系统角色列表
            const systemRoles = isRoleType
                ? ClusterRoleDetailPage.SYSTEM_ROLES_ROLE
                : ClusterRoleDetailPage.SYSTEM_ROLES_CLUSTERROLE;

            return systemRoles.includes(roleName);
        },
        isRoleType() {
            const routeQuery = this.data.get('route.query');
            return routeQuery?.type === 'role';
        },
    };

    initData() {
        return {
            klass: ['clusterrole-detail-view'],
            active: '',
            titleData: {
                backto: '',
                title: '',
                upper: [],
            },
            detail: {},
            loading: false,
            // YAML 对话框相关数据
            yamlDialogOpen: false,
            yamlDialogMode: 'view',
            yamlDialogTitle: '',
            yamlContent: '',
            currentEditRow: null,
        };
    }

    inited() {
        this.setTitleData();
    }

    attached() {
        this.getDetail();
    }

    onRegionChange() {
        redirect('/cce/#/cce/cluster/list');
    }

    // 查看YAML（只读模式）
    viewYaml() {
        const {detail} = this.data.get();
        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'view');
        this.data.set('yamlDialogTitle', '查看 YAML');
        this.data.set('currentEditRow', {yamlData: detail.yamlContent, metadata: detail});
        try {
            this.data.set('yamlContent', jsyaml.safeDump(detail.yamlContent));
        } catch (error) {
            Notification.error('YAML 序列化失败');
        }
    }

    // 修改YAML
    editYaml() {
        const {detail} = this.data.get();
        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'edit');
        this.data.set('yamlDialogTitle', '编辑 YAML');
        this.data.set('currentEditRow', {yamlData: detail.yamlContent, metadata: detail});
        try {
            this.data.set('yamlContent', jsyaml.safeDump(detail.yamlContent));
        } catch (error) {
            Notification.error('YAML 序列化失败');
        }
    }

    // YAML 弹框确认 - 复用列表页面的逻辑
    async onYamlConfirm() {
        try {
            const {yamlContent, yamlDialogMode, clusterUuid, currentEditRow} = this.data.get();
            const yamlData = jsyaml.load(yamlContent);
            const routeQuery = this.data.get('route.query');
            const isRoleType = routeQuery?.type === 'role';

            if (yamlDialogMode === 'edit') {
                const params = {
                    group: 'rbac.authorization.k8s.io',
                    version: 'v1',
                    kind: isRoleType ? 'Role' : 'ClusterRole',
                    name: currentEditRow.metadata?.name,
                    clusterUuid,
                };

                // 如果是 Role 类型，需要添加 namespace
                if (isRoleType && currentEditRow.metadata?.namespace) {
                    params.namespace = currentEditRow.metadata.namespace;
                }

                await this.$http.editResource(params, yamlData);
                Notification.success('编辑成功');
                this.getDetail(); // 重新获取详情数据
            }

            this.data.set('yamlDialogOpen', false);
        } catch (error) {
            Notification.error(error.message || '操作失败');
        }
    }

    // YAML 弹框关闭
    onYamlClose() {
        this.data.set('yamlDialogOpen', false);
        this.data.set('yamlContent', '');
        this.data.set('currentEditRow', null);
    }

    setTitleData() {
        const {clusterUuid, name, namespace, type} = this.data.get('route.query');
        this.data.set('clusterUuid', _.escape(clusterUuid));
        this.data.set('name', _.escape(name));

        // 如果是 Role 类型，设置 namespace
        if (type === 'role' && namespace) {
            this.data.set('namespace', _.escape(namespace));
        }

        // 根据资源类型设置不同的返回链接
        let backtoUrl = '/cce/cluster/role?clusterUuid=' + _.escape(clusterUuid);

        // 如果是 Role 类型，返回时激活 Role tab
        if (type === 'role') {
            backtoUrl += '&active=role';
        }

        this.data.set('titleData.backto', backtoUrl);
        this.data.set('titleData.title', _.escape(name) + '详情');

        // 根据类型设置不同的上级标题
        const resourceType = type === 'role' ? 'Role' : 'ClusterRole';
        this.data.set('titleData.upper', [_.escape(clusterUuid), resourceType]);
    }

    // 获取 ClusterRole 或 Role 详细信息
    async getDetail() {
        const {clusterUuid, name, namespace} = this.data.get();
        const routeQuery = this.data.get('route.query');
        const isRoleType = routeQuery?.type === 'role';

        this.data.set('loading', true);
        try {
            const params = {
                group: 'rbac.authorization.k8s.io',
                version: 'v1',
                kind: isRoleType ? 'Role' : 'ClusterRole',
                namespace: isRoleType ? namespace || '' : '', // Role 需要 namespace，ClusterRole 不需要
                clusterUuid,
                pageNo: 1,
                pageSize: 1000,
                name: name,
            };
            const response = await this.$http.getResourceObjList(params);

            if (response?.result?.items?.length > 0) {
                // 处理所有返回的项目
                const items = response.result.items
                    .map(item => {
                        try {
                            return typeof item === 'string' ? JSON.parse(item) : item;
                        } catch (error) {
                            return null;
                        }
                    })
                    .filter(Boolean);

                // 根据类型查找对应的资源
                let yamlData;
                if (isRoleType) {
                    // 找到指定名称和命名空间的 Role
                    yamlData = items.find(
                        item =>
                            item?.metadata?.name === name &&
                            item?.metadata?.namespace === namespace,
                    );
                } else {
                    // 找到指定名称的 ClusterRole
                    yamlData = items.find(item => item?.metadata?.name === name);
                }

                if (yamlData) {
                    const detail = {
                        name: yamlData?.metadata?.name,
                        namespace: yamlData?.metadata?.namespace, // Role 会有 namespace，ClusterRole 为 undefined
                        createTime: yamlData?.metadata?.creationTimestamp,
                        uid: yamlData?.metadata?.uid,
                        labels: this.formatLabels(yamlData?.metadata?.labels),
                        annotations: this.formatLabels(yamlData?.metadata?.annotations),
                        rules: yamlData?.rules || [],
                        yamlContent: yamlData,
                        clusterUuid,
                    };

                    this.data.set('detail', detail);
                }
            }
            this.data.set('loading', false);
        } catch (e) {
            this.data.set('loading', false);
        }
    }

    formatLabels(labels) {
        if (!labels || typeof labels !== 'object') {
            return [];
        }

        // 确保 labels 是一个对象而不是数组
        if (Array.isArray(labels)) {
            return labels;
        }

        const result = Object.entries(labels).map(([name, value]) => ({
            name: String(name),
            value: String(value),
        }));
        return result;
    }
}
