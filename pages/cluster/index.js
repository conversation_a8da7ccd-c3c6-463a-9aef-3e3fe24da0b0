/**
 *
 * @file pages/cluster/index.js
 * <AUTHOR>
 */

import './list/list';
import './detail/index';
import './snapshot/list';
import './snapshot/create';
import './namespace/create';
import './namespace/edit';
import './namespace/list/list';
import './namespace/detail/index';
import './namespace/detail/style.less';
import './create/create';
import './create-v2/create';
import './auto-scale/create/create';
import './auto-scale/detail/detail';
import './cordon/index';

import './role/index';
import './role/detail/index';

import './cluster-report/report';
import './cluster-report/style.less';

import './namespace/list/style.less';
import './snapshot/style.less';
import './list/style.less';
import './detail/style.less';
import './sms/style.less';
import './namespace/style.less';
import './create/style.less';
import './create-v2/style.less';
import './auto-scale/create/style.less';
import './auto-scale/detail/style.less';
import './cordon/style.less';

import './om-manage';

import './vk/create/create';
import './vk/create/style.less';
import './vk/list/list';
import './vk/list/style.less';

import './events-detail/events-detail';
import './events-detail/style.less';

import './delete/delete';
import './delete/style.less';

import './detail/subnet/style.less';

import './edit-tags/style.less';
import './edit-labels/style.less';
import './edit-taints/style.less';

import './auto-scale/stretch-strategy/style.less';
import './node-manage-detail/index';
