/**
 *
 * @file pages/index.js
 * <AUTHOR>
 */
import '@baiducloud/fe-common-style/bui/_bui_v4_overwrite.css';

import './overview/box/overview-box';
import './overview/style.less';
import './overview/box/style.less';
import './overview/new-overview';
import './namespace/list/list';
import './namespace/list/style.less';
import './namespace/list/tip-dialog';
import './namespace/list/tip-dialog/style.less';
import './namespace/create';
import './namespace/create/style.less';
import './namespace/update-notifyurl';
import './namespace/update-notifyurl/style.less';

import './cluster/index';
import './cluster/role/detail/index';

import './helm/index';
import './cicd';
import './cicd/style.less';

import './rbac';

import './image/user';
import './image/user/style.less';
import './image/tab/tab';
import './image/tab/style.less';
import './image/list/index';
import './image/list/list';
import './image/list/schema';
import './image/list/slot';
import './image/list/style.less';
import './image/create';
import './image/create/style.less';
import './image/github/account/list';
import './image/github/account/style.less';
import './image/github/callback';
import './image/github/style.less';
import './image/docker/index';
import './image/docker/list';
import './image/docker/schema';
import './image/docker/slot';
import './image/docker/style.less';
import './image/official/index';
import './image/official/list';
import './image/official/schema';
import './image/official/slot';
import './image/official/style.less';

import './workload';
import './storage';
import './application-create/create';
import './application-create/edit';
import './application-create/pv-create';
import './application-create/style.less';

import './instance/list/list';
import './instance/list/list-master';
import './instance/list/list-worker';
import './instance/list/style.less';
import './instance/events-detail/events-detail';
import './instance/events-detail/style.less';
import './instance/gpu-share/style.less';
import './instance/node-label/list';
import './instance/node-label/style.less';

import './group/create/create';
import './group/create/style.less';
import './group/list/style.less';
import './group/detail';
import './group/detail/style.less';
import './group/addExistNode';

import './remedy-rule';

import './service-group/node-pools/list';
import './service-group/node-pools/style.less';
import './service-group/node-pools/create/create';
import './service-group/node-pools/create/style.less';
import './service-group/access-group/list';
import './service-group/access-group/detail';
import './service-group/access-group/style.less';

import './service-group/batch-deployments/list/list';
import './service-group/batch-deployments/list/style.less';
import './service-group/batch-deployments/detail/detail';
import './service-group/batch-deployments/detail/style.less';

import './ai/aijob/list/list';
import './ai/aijob/list/style.less';

import './ai/aiqueue/list/list';
import './ai/aiqueue/list/style.less';

import './ai/aiqueue/create/create';
import './ai/aiqueue/create/style.less';
import './ai/aijob/create/create';
import './ai/aijob/create/success';
import './ai/aijob/create/style.less';
import './ai/aijob/detail/detail';
import './ai/aijob/detail/style.less';

import './cluster/upgrade/master';
import './cluster/upgrade/master/style.less';
import './cluster/upgrade/worker';
import './cluster/upgrade/worker/style.less';

import './ai/dataset/index';

import './ai/aijob/create/basic-config';
import './ai/aijob/create/basic-config/basic-info';
import './ai/aijob/create/basic-config/code-config';
import './ai/aijob/create/basic-config/datasource-config';
import './ai/aijob/create/basic-config/datasource-config-item';
import './ai/aijob/create/senior-config';
import './ai/aijob/create/senior-config/venchor-item';

import './backup-center/index';

import './cluster/create-v2/components/node/resource-reserved';
